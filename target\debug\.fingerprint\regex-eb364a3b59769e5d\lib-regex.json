{"rustc": 1842507548689473721, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 8487520173250820266, "deps": [[555019317135488525, "regex_automata", false, 218994158586071674], [2779309023524819297, "aho_corasick", false, 14710989264116451946], [9408802513701742484, "regex_syntax", false, 2083046449984360537], [15932120279885307830, "memchr", false, 15998399935219876739]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-eb364a3b59769e5d\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}