use std::env;
use tokio::sync::broadcast;
use reqwest::Client;
use serde_json;

use crate::types::{TranslatedAnnouncement, Message, Result, AppError};
use crate::ai_translator::AITranslator;

pub struct BarkNotifier {
    client: Client,
    bark_urls: Vec<String>,
    bark_volume: u8,
    bark_group: String,
    bark_sound: String,
    bark_level: String,
}

impl BarkNotifier {
    pub fn new() -> Result<Self> {
        // 从环境变量读取 Bark URL 列表，支持多个 URL 用逗号分隔
        let bark_url_env = env::var("BARK_URL")
            .map_err(|_| AppError::Config("BARK_URL not found in environment".to_string()))?;

        let bark_urls: Vec<String> = bark_url_env
            .split(',')
            .map(|url| url.trim().to_string())
            .filter(|url| !url.is_empty())
            .collect();

        if bark_urls.is_empty() {
            return Err(AppError::Config("No valid BARK_URL found".to_string()));
        }

        // 读取可配置的 Bark 参数
        let bark_volume = env::var("BARK_VOLUME")
            .unwrap_or_else(|_| "7".to_string())
            .parse::<u8>()
            .unwrap_or(7)
            .clamp(0, 10);

        let bark_group = env::var("BARK_GROUP")
            .unwrap_or_else(|_| "BIN通知公告".to_string());

        let bark_sound = env::var("BARK_SOUND")
            .unwrap_or_else(|_| "spell".to_string());

        let bark_level = env::var("BARK_LEVEL")
            .unwrap_or_else(|_| "critical".to_string());

        Ok(Self {
            client: Client::new(),
            bark_urls,
            bark_volume,
            bark_group,
            bark_sound,
            bark_level,
        })
    }

    /// 运行 Bark 通知器
    pub async fn run(
        &self,
        mut message_rx: broadcast::Receiver<Message>,
        mut shutdown_rx: broadcast::Receiver<()>,
    ) -> Result<()> {
        println!("📱 Bark Notifier started");
        println!("📱 Configured {} Bark device(s)", self.bark_urls.len());

        loop {
            tokio::select! {
                // 接收翻译后的公告消息
                msg = message_rx.recv() => {
                    match msg {
                        Ok(Message::TranslatedAnnouncement(translated_announcement)) => {
                            println!("📱 Received translated announcement for notification: {}", translated_announcement.translated_title);
                            
                            match self.send_notification(translated_announcement).await {
                                Ok(_) => {
                                    println!("✅ Notification sent successfully");
                                }
                                Err(e) => {
                                    eprintln!("❌ Failed to send notification: {}", e);
                                }
                            }
                        }
                        Ok(Message::Shutdown) => {
                            println!("🛑 Bark Notifier received shutdown signal");
                            break;
                        }
                        Ok(_) => {
                            // 忽略其他消息类型
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            eprintln!("⚠️ Bark Notifier lagged, skipped {} messages", skipped);
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            println!("📡 Message channel closed, shutting down Bark Notifier");
                            break;
                        }
                    }
                }
                // 处理关闭信号
                _ = shutdown_rx.recv() => {
                    println!("🛑 Bark Notifier shutdown signal received");
                    break;
                }
            }
        }

        println!("👋 Bark Notifier shutdown complete");
        Ok(())
    }

    /// 发送通知
    async fn send_notification(&self, announcement: TranslatedAnnouncement) -> Result<()> {
        println!("📤 Sending notification: {}", announcement.translated_title);

        // 使用AI返回的标题
        let title = announcement.translated_title;

        // 格式化时间为 UTC+8，包含毫秒精度
        let formatted_time = AITranslator::format_time_utc8_with_millis(&announcement.publish_time);

        // 组合内容：AI概要 + 换行 + 时间
        let body = format!("{}\n{}", announcement.summary, formatted_time);

        // 打印Bark发送的具体内容
        println!("📱 Bark推送内容详情:");
        println!("   📋 标题: {}", title);
        println!("   📝 内容: {}", body);
        println!("   🕒 发布时间: {}", formatted_time);
        println!("   📊 摘要长度: {} 字符", announcement.summary.len());

        // 调用 Bark API
        self.call_bark_api(&title, &body).await?;

        Ok(())
    }

    /// 调用 Bark API 发送通知
    async fn call_bark_api(&self, title: &str, body: &str) -> Result<()> {
        println!("📱 正在发送 Bark 推送通知到 {} 个设备...", self.bark_urls.len());

        let mut success_count = 0;
        
        for (i, bark_url) in self.bark_urls.iter().enumerate() {
            match self.send_to_single_device(bark_url, title, body, i + 1).await {
                Ok(_) => {
                    success_count += 1;
                    println!("📱 ✅ Bark 推送发送成功 (设备 {})", i + 1);
                }
                Err(e) => {
                    eprintln!("📱 ❌ Bark 推送失败 (设备 {}): {}", i + 1, e);
                }
            }
        }

        if success_count > 0 {
            println!("📱 ✅ 成功发送到 {}/{} 个设备", success_count, self.bark_urls.len());
            Ok(())
        } else {
            Err(AppError::Notification("所有设备推送失败".to_string()))
        }
    }

    /// 发送到单个设备
    async fn send_to_single_device(&self, bark_url: &str, title: &str, body: &str, _device_num: usize) -> Result<()> {
        // 从 URL 中提取 device_key
        let device_key = bark_url
            .trim_end_matches('/')
            .split('/')
            .last()
            .ok_or_else(|| AppError::Config("Invalid BARK_URL format".to_string()))?;

        // 构建 Bark 服务器基础 URL
        let base_url = bark_url
            .trim_end_matches('/')
            .rsplitn(2, '/')
            .nth(1)
            .ok_or_else(|| AppError::Config("Invalid BARK_URL format".to_string()))?;

        let api_url = format!("{}/push", base_url);

        // 构建 JSON 请求体
        let mut payload = serde_json::json!({
            "device_key": device_key,
            "title": title,
            "body": body,
            "level": self.bark_level,
            "sound": self.bark_sound,
            "group": self.bark_group
        });

        // 只有当音量不为默认值时才添加 volume 参数
        if self.bark_volume != 5 {
            payload["volume"] = serde_json::Value::Number(serde_json::Number::from(self.bark_volume));
        }

        let response = self.client
            .post(&api_url)
            .header("Content-Type", "application/json; charset=utf-8")
            .json(&payload)
            .timeout(std::time::Duration::from_secs(10))
            .send()
            .await?;

        if response.status().is_success() {
            // 尝试解析响应
            if let Ok(result) = response.json::<serde_json::Value>().await {
                if result.get("code").and_then(|c| c.as_u64()) == Some(200) {
                    return Ok(());
                } else {
                    let message = result.get("message")
                        .and_then(|m| m.as_str())
                        .unwrap_or("Unknown error");
                    return Err(AppError::Notification(format!("Bark API error: {}", message)));
                }
            }
            Ok(())
        } else {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            Err(AppError::Notification(format!("HTTP error {}: {}", status, error_text)))
        }
    }
}
