{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 14264657120815013845, "deps": [[784494742817713399, "tower_service", false, 2442481590079044307], [1906322745568073236, "pin_project_lite", false, 2183852692003433065], [4121350475192885151, "iri_string", false, 13604826457974586065], [5695049318159433696, "tower", false, 5637728309425057407], [7712452662827335977, "tower_layer", false, 12450596261126197733], [7896293946984509699, "bitflags", false, 17161137190823567868], [9010263965687315507, "http", false, 15083974679218805705], [10629569228670356391, "futures_util", false, 10746205587291257171], [14084095096285906100, "http_body", false, 18172010706914083594], [16066129441945555748, "bytes", false, 3429117579287067157]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-997dcda38176d375\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}