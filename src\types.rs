use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 原始公告数据（从 Binance WebSocket 接收）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RawAnnouncement {
    pub title: String,
    pub body: String,
    pub category_id: u32,
    pub category_name: String,
    pub publish_time: DateTime<Utc>,
    pub disclaimer: Option<String>,
}

/// 翻译后的公告数据（AI 处理后）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TranslatedAnnouncement {
    pub original_title: String,
    pub translated_title: String,
    pub original_body: String,
    pub translated_body: String,
    pub summary: String,
    pub category_name: String,
    pub publish_time: DateTime<Utc>,
    pub disclaimer: Option<String>,
}

/// 消息类型枚举
#[derive(Debug, <PERSON>lone)]
pub enum Message {
    /// 原始公告（WebSocket -> AI Translator）
    RawAnnouncement(RawAnnouncement),
    /// 翻译后的公告（AI Translator -> Bark Notifier）
    TranslatedAnnouncement(TranslatedAnnouncement),
    /// 关闭信号
    #[allow(dead_code)]
    Shutdown,
}

/// 应用程序错误类型
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("WebSocket error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),
    
    #[error("HTTP error: {0}")]
    Http(#[from] reqwest::Error),
    
    #[error("JSON parsing error: {0}")]
    Json(#[from] serde_json::Error),
    
    #[error("Environment variable error: {0}")]
    Env(#[from] std::env::VarError),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("URL parse error: {0}")]
    UrlParse(#[from] url::ParseError),

    #[error("HTTP build error: {0}")]
    HttpBuild(#[from] tokio_tungstenite::tungstenite::http::Error),
    
    #[error("Channel send error")]
    #[allow(dead_code)]
    ChannelSend,

    #[error("Channel receive error")]
    #[allow(dead_code)]
    ChannelReceive,
    
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("AI service error: {0}")]
    AiService(String),
    
    #[error("Notification error: {0}")]
    Notification(String),

    #[error("General error: {0}")]
    General(String),
}

impl From<String> for AppError {
    fn from(msg: String) -> Self {
        AppError::General(msg)
    }
}

pub type Result<T> = std::result::Result<T, AppError>;
