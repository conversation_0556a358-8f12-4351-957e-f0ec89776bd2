{"rustc": 1842507548689473721, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 15657897354478470176, "path": 10234824174975898059, "deps": [[99287295355353247, "data_encoding", false, 10149458900510899217], [4359956005902820838, "utf8", false, 15229345958676388220], [5986029879202738730, "log", false, 7463655833432837285], [6163892036024256188, "httparse", false, 2780161269485313973], [9010263965687315507, "http", false, 3736580640712029992], [10724389056617919257, "sha1", false, 4649245176712075437], [10806645703491011684, "thiserror", false, 4834775947624535655], [11916940916964035392, "rand", false, 18260110424499197625], [16066129441945555748, "bytes", false, 7899407690149537674], [16785601910559813697, "native_tls_crate", false, 756758295800197205]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-f36911b0d2f59ef6\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}