use chrono::{DateTime, Utc};
use hmac::{Hmac, Mac};
use serde::{Deserialize, Serialize};
use sha2::Sha256;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::{Mutex, broadcast};
use tokio::time::{sleep, Duration};
use tokio_tungstenite::{connect_async, tungstenite::Message as WsMessage};
use url::Url;

use crate::types::{RawAnnouncement, Message, Result};

type HmacSha256 = Hmac<Sha256>;

#[derive(Debug, Serialize, Deserialize)]
struct WebSocketResponse {
    #[serde(rename = "type")]
    response_type: String,
    topic: Option<String>,
    data: Option<String>,
    #[serde(rename = "subType")]
    sub_type: Option<String>,
    code: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct AnnouncementData {
    #[serde(rename = "catalogId")]
    catalog_id: u32,
    #[serde(rename = "catalogName")]
    catalog_name: String,
    #[serde(rename = "publishDate")]
    publish_date: u64,
    title: String,
    body: String,
    #[serde(default)]
    disclaimer: Option<String>,
}

#[derive(Debug, Serialize)]
struct SubscribeCommand {
    command: String,
    value: String,
}

#[derive(Debug, Serialize)]
struct UnsubscribeCommand {
    command: String,
    value: String,
}

pub struct BinanceWebSocketClient {
    api_key: String,
    secret_key: String,
    base_url: String,
    last_message_time: Arc<Mutex<std::time::Instant>>,
    connection_start_time: Arc<Mutex<std::time::Instant>>,
    reconnect_count: Arc<Mutex<u64>>,
    total_uptime: Arc<Mutex<Duration>>,
    message_sender: Option<Arc<broadcast::Sender<Message>>>,
}

impl BinanceWebSocketClient {
    pub fn new(api_key: String, secret_key: String) -> Self {
        Self {
            api_key,
            secret_key,
            base_url: "wss://api.binance.com/sapi/wss".to_string(),
            last_message_time: Arc::new(Mutex::new(std::time::Instant::now())),
            connection_start_time: Arc::new(Mutex::new(std::time::Instant::now())),
            reconnect_count: Arc::new(Mutex::new(0)),
            total_uptime: Arc::new(Mutex::new(Duration::from_secs(0))),
            message_sender: None,
        }
    }

    pub fn with_sender(mut self, sender: Arc<broadcast::Sender<Message>>) -> Self {
        self.message_sender = Some(sender);
        self
    }

    fn generate_signature(&self, query_string: &str) -> String {
        let mut mac = HmacSha256::new_from_slice(self.secret_key.as_bytes())
            .expect("HMAC can take key of any size");
        mac.update(query_string.as_bytes());
        hex::encode(mac.finalize().into_bytes())
    }

    fn generate_random_string(&self) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        SystemTime::now().hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    // Rate limiting: Maximum 5 messages per second as per official documentation
    async fn rate_limit_check(&self) {
        let mut last_time = self.last_message_time.lock().await;
        let now = std::time::Instant::now();
        let elapsed = now.duration_since(*last_time);

        // Ensure at least 200ms between messages (5 messages per second = 1000ms/5 = 200ms)
        if elapsed < Duration::from_millis(200) {
            let sleep_duration = Duration::from_millis(200) - elapsed;
            drop(last_time); // Release the lock before sleeping
            sleep(sleep_duration).await;
            *self.last_message_time.lock().await = std::time::Instant::now();
        } else {
            *last_time = now;
        }
    }

    // Reset connection start time and update statistics
    async fn reset_connection_time(&self) {
        let now = std::time::Instant::now();
        let mut start_time = self.connection_start_time.lock().await;

        // Add previous session uptime to total
        if *start_time != std::time::Instant::now() {
            let session_uptime = now.duration_since(*start_time);
            *self.total_uptime.lock().await += session_uptime;
        }

        *start_time = now;
    }

    // Increment reconnect counter
    async fn increment_reconnect_count(&self) {
        *self.reconnect_count.lock().await += 1;
    }

    // Get connection statistics
    async fn get_stats(&self) -> (u64, Duration, Duration) {
        let reconnect_count = *self.reconnect_count.lock().await;
        let total_uptime = *self.total_uptime.lock().await;
        let current_session = self.connection_start_time.lock().await.elapsed();
        (reconnect_count, total_uptime, current_session)
    }

    // Log connection statistics
    async fn log_stats(&self, reason: &str) {
        let (reconnects, total_uptime, current_session) = self.get_stats().await;
        let total_hours = total_uptime.as_secs() / 3600;
        let total_minutes = (total_uptime.as_secs() % 3600) / 60;
        let session_hours = current_session.as_secs() / 3600;
        let session_minutes = (current_session.as_secs() % 3600) / 60;

        println!("📊 Connection Statistics [{}]:", reason);
        println!("   🔄 Total Reconnects: {}", reconnects);
        println!("   ⏱️  Total Uptime: {}h {}m", total_hours, total_minutes);
        println!("   📅 Current Session: {}h {}m", session_hours, session_minutes);
        println!("   🎯 Availability: {:.2}%", if reconnects == 0 { 100.0 } else {
            (total_uptime.as_secs() as f64 / (total_uptime.as_secs() + reconnects * 10) as f64) * 100.0
        });
    }

    fn build_connection_url(&self, topics: &[&str]) -> String {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();

        let random = self.generate_random_string();
        let topic = topics.join("|");
        let recv_window = 30000; // Within the maximum limit of 60000ms

        // Build parameters in alphabetical order as required by the API
        // According to official docs: random, recvWindow, timestamp, topic (alphabetical)
        let params = vec![
            ("random", random),
            ("recvWindow", recv_window.to_string()),
            ("timestamp", timestamp.to_string()),
            ("topic", topic),
        ];

        // Create query string for signature (parameters already in alphabetical order)
        let query_string = params
            .iter()
            .map(|(k, v)| format!("{}={}", k, v))
            .collect::<Vec<_>>()
            .join("&");

        // Generate signature
        let signature = self.generate_signature(&query_string);

        // Build final URL with signature appended
        format!("{}?{}&signature={}", self.base_url, query_string, signature)
    }

    async fn connect_and_subscribe(&self, topics: &[&str], mut shutdown_rx: broadcast::Receiver<()>) -> Result<bool> {
        let url = self.build_connection_url(topics);
        println!("Connecting to: {}", url);

        let url = Url::parse(&url)?;

        // Create request with proper WebSocket headers
        let request = tokio_tungstenite::tungstenite::http::Request::builder()
            .uri(url.as_str())
            .header("Host", url.host_str().unwrap_or("api.binance.com"))
            .header("X-MBX-APIKEY", &self.api_key)
            .header("Connection", "Upgrade")
            .header("Upgrade", "websocket")
            .header("Sec-WebSocket-Version", "13")
            .header("Sec-WebSocket-Key", tokio_tungstenite::tungstenite::handshake::client::generate_key())
            .body(())?;

        let (ws_stream, response) = connect_async(request).await.map_err(|e| {
            eprintln!("❌ WebSocket connection failed: {}", e);
            e
        })?;

        // Reset connection start time and log success
        self.reset_connection_time().await;

        println!("✅ WebSocket connection established!");
        println!("📊 Server response: {:?}", response.status());
        println!("📅 Connection valid for up to 24 hours (will auto-reconnect at 23 hours)");

        // Log current statistics
        self.log_stats("Connection Established").await;

        let (write, mut read) = ws_stream.split();
        let write = Arc::new(Mutex::new(write));

        // Subscribe to topics with rate limiting
        for topic in topics {
            let subscribe_cmd = SubscribeCommand {
                command: "SUBSCRIBE".to_string(),
                value: topic.to_string(),
            };
            let subscribe_msg = serde_json::to_string(&subscribe_cmd)?;
            println!("Subscribing to topic: {}", topic);

            // Apply rate limiting before sending message
            self.rate_limit_check().await;

            use futures_util::SinkExt;
            let mut writer = write.lock().await;
            writer.send(WsMessage::Text(subscribe_msg.into())).await?;
        }

        // Start ping task using shared writer with rate limiting
        // Send PING every 20 seconds (more frequent than the required 60s to ensure connection stability)
        let ping_write = Arc::clone(&write);
        let ping_rate_limiter = Arc::clone(&self.last_message_time);
        tokio::spawn(async move {
            loop {
                sleep(Duration::from_secs(20)).await;

                // Apply rate limiting for ping messages
                let mut last_time = ping_rate_limiter.lock().await;
                let now = std::time::Instant::now();
                let elapsed = now.duration_since(*last_time);

                if elapsed < Duration::from_millis(200) {
                    let sleep_duration = Duration::from_millis(200) - elapsed;
                    drop(last_time);
                    sleep(sleep_duration).await;
                    *ping_rate_limiter.lock().await = std::time::Instant::now();
                } else {
                    *last_time = now;
                }

                use futures_util::SinkExt;
                let mut writer = ping_write.lock().await;
                if let Err(e) = writer.send(WsMessage::Ping(vec![].into())).await {
                    eprintln!("Failed to send ping: {}", e);
                    break;
                }
                println!("Ping sent");
            }
        });

        // Start connection time monitoring task
        let connection_monitor = Arc::clone(&self.connection_start_time);
        let (reconnect_tx, mut reconnect_rx) = broadcast::channel(1);
        tokio::spawn(async move {
            loop {
                sleep(Duration::from_secs(60)).await; // Check every minute
                let connection_start = connection_monitor.lock().await;
                let elapsed = connection_start.elapsed();

                // Auto-reconnect after 23 hours (82800 seconds)
                if elapsed >= Duration::from_secs(23 * 60 * 60) { // 23 hours
                    println!("⏰ Connection has been active for 23 hours, initiating reconnection...");
                    let _ = reconnect_tx.send(());
                    break;
                }

                // Log connection uptime every hour for the first 24 hours
                let hours = elapsed.as_secs() / 3600;
                let minutes = (elapsed.as_secs() % 3600) / 60;

                // Log every hour after the first hour
                if elapsed.as_secs() >= 3600 && elapsed.as_secs() % 3600 < 60 {
                    if hours < 23 {
                        println!("📊 Connection uptime: {} hours {} minutes", hours, minutes);
                    } else {
                        println!("📊 Connection uptime: {} hours {} minutes (will reconnect soon)", hours, minutes);
                    }
                }
            }
        });

        // Listen for messages with graceful shutdown and auto-reconnect support
        use futures_util::StreamExt;
        loop {
            tokio::select! {
                // Handle incoming WebSocket messages
                msg = read.next() => {
                    match msg {
                        Some(Ok(WsMessage::Text(text))) => {
                            self.handle_message(&text).await;
                        }
                        Some(Ok(WsMessage::Pong(_))) => {
                            println!("Received pong");
                        }
                        Some(Ok(WsMessage::Close(_))) => {
                            println!("Connection closed by server");
                            break;
                        }
                        Some(Err(e)) => {
                            eprintln!("❌ Error receiving message: {}", e);
                            self.log_stats("Connection Error").await;
                            return Ok(false); // Indicate reconnection needed
                        }
                        None => {
                            println!("⚠️ WebSocket stream ended unexpectedly");
                            self.log_stats("Stream Ended").await;
                            return Ok(false); // Indicate reconnection needed
                        }
                        _ => {}
                    }
                }
                // Handle auto-reconnect signal (23 hours elapsed)
                _ = reconnect_rx.recv() => {
                    println!("🔄 Auto-reconnect triggered after 23 hours, closing current connection...");

                    // Gracefully unsubscribe before reconnecting
                    for topic in topics {
                        let unsubscribe_cmd = UnsubscribeCommand {
                            command: "UNSUBSCRIBE".to_string(),
                            value: topic.to_string(),
                        };
                        if let Ok(unsubscribe_msg) = serde_json::to_string(&unsubscribe_cmd) {
                            self.rate_limit_check().await;
                            use futures_util::SinkExt;
                            let mut writer = write.lock().await;
                            if let Err(e) = writer.send(WsMessage::Text(unsubscribe_msg.into())).await {
                                eprintln!("Failed to send unsubscribe message during reconnect: {}", e);
                            } else {
                                println!("✅ Unsubscribed from topic: {} (auto-reconnect)", topic);
                            }
                        }
                    }

                    // Send close frame
                    {
                        use futures_util::SinkExt;
                        let mut writer = write.lock().await;
                        if let Err(e) = writer.send(WsMessage::Close(None)).await {
                            eprintln!("Failed to send close frame during reconnect: {}", e);
                        } else {
                            println!("✅ Close frame sent (auto-reconnect)");
                        }
                    }

                    println!("🔄 Auto-reconnect cleanup completed, will establish new connection...");
                    self.log_stats("23-Hour Auto-Reconnect").await;
                    return Ok(false); // Indicate reconnection needed
                }
                // Handle shutdown signal
                _ = shutdown_rx.recv() => {
                    println!("🛑 Shutdown signal received, unsubscribing from topics...");

                    // Unsubscribe from all topics before closing with rate limiting
                    for topic in topics {
                        let unsubscribe_cmd = UnsubscribeCommand {
                            command: "UNSUBSCRIBE".to_string(),
                            value: topic.to_string(),
                        };
                        if let Ok(unsubscribe_msg) = serde_json::to_string(&unsubscribe_cmd) {
                            // Apply rate limiting before sending unsubscribe message
                            self.rate_limit_check().await;

                            use futures_util::SinkExt;
                            let mut writer = write.lock().await;
                            if let Err(e) = writer.send(WsMessage::Text(unsubscribe_msg.into())).await {
                                eprintln!("Failed to send unsubscribe message: {}", e);
                            } else {
                                println!("✅ Unsubscribed from topic: {}", topic);
                            }
                        }
                    }

                    // Send close frame
                    {
                        use futures_util::SinkExt;
                        let mut writer = write.lock().await;
                        if let Err(e) = writer.send(WsMessage::Close(None)).await {
                            eprintln!("Failed to send close frame: {}", e);
                        } else {
                            println!("✅ Close frame sent");
                        }
                    }

                    println!("🔄 Graceful shutdown completed");
                    self.log_stats("Manual Shutdown").await;
                    return Ok(true); // Indicate clean shutdown, no reconnection needed
                }
            }
        }

        // This should never be reached, but just in case
        println!("⚠️ Unexpected exit from message loop");
        self.log_stats("Unexpected Exit").await;
        Ok(false)
    }

    async fn handle_message(&self, text: &str) {
        match serde_json::from_str::<WebSocketResponse>(text) {
            Ok(response) => {
                match response.response_type.as_str() {
                    "COMMAND" => {
                        println!("Command response: {:?}", response);
                    }
                    "DATA" => {
                        if let Some(data) = response.data {
                            self.handle_announcement_data(&data).await;
                        }
                    }
                    _ => {
                        println!("Unknown response type: {:?}", response);
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to parse message: {}", e);
                println!("Raw message: {}", text);
            }
        }
    }

    async fn handle_announcement_data(&self, data: &str) {
        match serde_json::from_str::<AnnouncementData>(data) {
            Ok(announcement) => {
                let publish_time = DateTime::from_timestamp_millis(announcement.publish_date as i64)
                    .unwrap_or_else(|| Utc::now());

                println!("\n🔔 NEW BINANCE ANNOUNCEMENT 🔔");
                println!("📂 Category: {} (ID: {})", announcement.catalog_name, announcement.catalog_id);
                println!("📅 Published: {}", publish_time.format("%Y-%m-%d %H:%M:%S UTC"));
                println!("📰 Title: {}", announcement.title);
                println!("📝 Content: {}", announcement.body);
                if let Some(disclaimer) = &announcement.disclaimer {
                    if !disclaimer.is_empty() {
                        println!("⚠️  Disclaimer: {}", disclaimer);
                    }
                }
                println!("{}", "=".repeat(80));

                // 发送到 AI 翻译器
                if let Some(sender) = &self.message_sender {
                    let raw_announcement = RawAnnouncement {
                        title: announcement.title,
                        body: announcement.body,
                        category_id: announcement.catalog_id,
                        category_name: announcement.catalog_name,
                        publish_time,
                        disclaimer: announcement.disclaimer,
                    };

                    if let Err(e) = sender.send(Message::RawAnnouncement(raw_announcement)) {
                        eprintln!("❌ Failed to send announcement to AI translator: {}", e);
                    } else {
                        println!("✅ Announcement sent to AI translator");
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to parse announcement data: {}", e);
                println!("Raw data: {}", data);
            }
        }
    }

    /// 发送测试公告（用于测试）
    pub async fn send_test_announcement(&self) -> Result<()> {
        if let Some(sender) = &self.message_sender {
            let test_announcement = RawAnnouncement {
                title: "Binance Announces New Trading Competition with $100,000 Prize Pool".to_string(),
                body: "Dear Binancians, We are excited to announce a new trading competition starting from 2025-07-28 12:00 UTC to 2025-08-28 12:00 UTC. Participants can compete for a share of the $100,000 prize pool by trading selected cryptocurrencies. To participate: 1) Register for the competition 2) Trade minimum $50 worth of eligible tokens 3) Maintain positive PnL during the competition period. Winners will be announced within 7 days after the competition ends. Terms and conditions apply.".to_string(),
                category_id: 93,
                category_name: "Latest Activities".to_string(),
                publish_time: Utc::now(),
                disclaimer: Some("This is a test announcement for development purposes.".to_string()),
            };

            if let Err(e) = sender.send(Message::RawAnnouncement(test_announcement)) {
                eprintln!("❌ Failed to send test announcement: {}", e);
            } else {
                println!("✅ Test announcement sent successfully");
            }
        }
        Ok(())
    }

    /// 运行 WebSocket 客户端
    pub async fn run(&self, mut shutdown_rx: broadcast::Receiver<()>) -> Result<()> {
        let topics = vec!["com_announcement_en"];
        let mut consecutive_failures = 0u32;
        let mut last_success_time = std::time::Instant::now();

        loop {
            let (reconnects, _total_uptime, _) = self.get_stats().await;
            println!("🔄 Attempting connection #{} (Total reconnects: {})", reconnects + 1, reconnects);

            let shutdown_rx_clone = shutdown_rx.resubscribe();

            match self.connect_and_subscribe(&topics, shutdown_rx_clone).await {
                Ok(should_exit) => {
                    if should_exit {
                        println!("✅ WebSocket client graceful shutdown completed");
                        break;
                    } else {
                        consecutive_failures = 0;
                        last_success_time = std::time::Instant::now();
                        self.increment_reconnect_count().await;
                        println!("🔄 Connection ended, preparing to reconnect...");
                        sleep(Duration::from_secs(2)).await;
                    }
                }
                Err(e) => {
                    consecutive_failures += 1;
                    eprintln!("❌ Connection error #{}: {}", consecutive_failures, e);
                    self.increment_reconnect_count().await;
                    self.log_stats("Connection Failed").await;

                    let base_delay = 5;
                    let max_delay = 300;
                    let delay = std::cmp::min(base_delay * 2_u64.pow(consecutive_failures.min(6)), max_delay);

                    println!("⏳ Retrying in {} seconds... (Consecutive failures: {})", delay, consecutive_failures);

                    if consecutive_failures >= 5 && last_success_time.elapsed() > Duration::from_secs(600) {
                        println!("🚨 No successful connection for 10+ minutes. Using extended retry delay...");
                        let extended_delay = std::cmp::min(delay * 2, 600);

                        tokio::select! {
                            _ = sleep(Duration::from_secs(extended_delay)) => {
                                println!("🔄 Extended retry period completed, attempting reconnection...");
                            }
                            _ = shutdown_rx.recv() => {
                                println!("🛑 Shutdown requested during extended retry, exiting...");
                                break;
                            }
                        }
                        continue;
                    }

                    tokio::select! {
                        _ = sleep(Duration::from_secs(delay)) => {
                            println!("🔄 Retry delay completed, attempting reconnection...");
                        }
                        _ = shutdown_rx.recv() => {
                            println!("🛑 Shutdown requested during retry wait, exiting...");
                            break;
                        }
                    }
                }
            }
        }

        Ok(())
    }
}
