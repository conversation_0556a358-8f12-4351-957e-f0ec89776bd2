{"rustc": 1842507548689473721, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 8487520173250820266, "deps": [[555019317135488525, "regex_automata", false, 14275135670777280301], [2779309023524819297, "aho_corasick", false, 8877029750048118022], [9408802513701742484, "regex_syntax", false, 10990357974565177469], [15932120279885307830, "memchr", false, 17818124885875235882]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-1257831fc674eced\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}