{"rustc": 1842507548689473721, "features": "[\"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"stream\", \"tokio-native-tls\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"url\", \"webpki-roots\"]", "target": 10194999948271016277, "profile": 2241668132362809309, "path": 3942086151908485922, "deps": [[4254813506600451364, "tungstenite", false, 8299600898776015886], [5986029879202738730, "log", false, 2224308386814708378], [10629569228670356391, "futures_util", false, 10746205587291257171], [12186126227181294540, "tokio_native_tls", false, 14705051911432587782], [16785601910559813697, "native_tls_crate", false, 1176033907174144812], [17531218394775549125, "tokio", false, 8921705629539571435]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-tungstenite-1c731b58dd8246cd\\dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}