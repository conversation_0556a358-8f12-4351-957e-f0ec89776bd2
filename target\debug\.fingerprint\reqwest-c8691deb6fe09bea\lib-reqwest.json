{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 8687871822414892605, "deps": [[40386456601120721, "percent_encoding", false, 9377752627120928217], [41016358116313498, "hyper_util", false, 10016379999256526863], [784494742817713399, "tower_service", false, 18402585402976662615], [1906322745568073236, "pin_project_lite", false, 17143027058502063303], [2054153378684941554, "tower_http", false, 17282084707480096826], [2517136641825875337, "sync_wrapper", false, 14551237430480884179], [2883436298747778685, "rustls_pki_types", false, 17563989362260641558], [3150220818285335163, "url", false, 7335035297232577382], [5695049318159433696, "tower", false, 14911688062930947927], [5986029879202738730, "log", false, 7463655833432837285], [7620660491849607393, "futures_core", false, 14401924481682515838], [9010263965687315507, "http", false, 3736580640712029992], [9689903380558560274, "serde", false, 5904387046991183924], [10229185211513642314, "mime", false, 8422878397012588219], [11957360342995674422, "hyper", false, 2381224845544729525], [12186126227181294540, "tokio_native_tls", false, 12485078710191466671], [13077212702700853852, "base64", false, 14820960816727599856], [14084095096285906100, "http_body", false, 1581608618866217959], [14359893265615549706, "h2", false, 3554830748134993810], [14564311161534545801, "encoding_rs", false, 552875020012463598], [16066129441945555748, "bytes", false, 7899407690149537674], [16362055519698394275, "serde_json", false, 520667598245768627], [16542808166767769916, "serde_urlencoded", false, 12427597663011634058], [16785601910559813697, "native_tls_crate", false, 756758295800197205], [16900715236047033623, "http_body_util", false, 3525001053293964557], [17531218394775549125, "tokio", false, 9633873629009186503], [18273243456331255970, "hyper_tls", false, 7926998887578377654]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-c8691deb6fe09bea\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}