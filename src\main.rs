
mod types;
mod websocket_client;
mod ai_translator;
mod bark_notifier;

use std::env;
use std::sync::Arc;
use tokio::sync::broadcast;

use types::{Message, Result};
use websocket_client::BinanceWebSocketClient;
use ai_translator::AITranslator;
use bark_notifier::BarkNotifier;

#[tokio::main]
async fn main() -> Result<()> {
    // 检查是否为测试模式
    let args: Vec<String> = env::args().collect();
    let test_mode = args.len() > 1 && args[1] == "--test";

    // 加载环境变量
    if let Err(e) = dotenvy::dotenv() {
        println!("⚠️  Warning: Could not load .env file: {}", e);
        println!("💡 Make sure .env file exists with required environment variables");
    } else {
        println!("✅ .env file loaded successfully");
    }

    // 初始化日志
    env_logger::init();

    // 测试信号处理器（仅在非测试模式下）
    #[cfg(unix)]
    if !test_mode {
        if let Err(e) = setup_signal_handlers().await {
            eprintln!("❌ Signal handler setup failed: {}", e);
            return Err(e.into());
        }
    }

    if test_mode {
        println!("🧪 Running in TEST MODE");
        return run_test_mode().await;
    }

    // 加载配置
    let binance_api_key = env::var("BINANCE_API_KEY")
        .expect("BINANCE_API_KEY environment variable is required");
    let binance_secret_key = env::var("BINANCE_SECRET_KEY")
        .expect("BINANCE_SECRET_KEY environment variable is required");

    println!("🚀 Starting Binance WebSocket Notifier v2.0");
    println!("📡 API Key: {}...", &binance_api_key[..8]);
    println!("🎯 Features: WebSocket + AI Translation + Bark Notification");
    println!("💡 Press Ctrl+C to gracefully shutdown");
    println!("🐧 Running on Unix-like system (Debian)");
    println!("🔧 Process ID: {}", std::process::id());
    println!("{}", "=".repeat(60));

    // 创建消息通道
    let (message_tx, _) = broadcast::channel::<Message>(1000);
    let message_tx = Arc::new(message_tx);

    // 创建关闭信号通道
    let (shutdown_tx, _) = broadcast::channel(1);

    // 设置信号处理 - 改进版本，确保信号能被正确捕获
    let shutdown_tx_clone = shutdown_tx.clone();
    let signal_handle = tokio::spawn(async move {
        #[cfg(unix)]
        {
            use tokio::signal::unix::{signal, SignalKind};

            // 创建信号处理器，增加错误处理
            let mut sigterm = match signal(SignalKind::terminate()) {
                Ok(s) => s,
                Err(e) => {
                    eprintln!("❌ Failed to create SIGTERM handler: {}", e);
                    return;
                }
            };

            let mut sigint = match signal(SignalKind::interrupt()) {
                Ok(s) => s,
                Err(e) => {
                    eprintln!("❌ Failed to create SIGINT handler: {}", e);
                    return;
                }
            };

            println!("✅ Signal handlers initialized successfully");

            tokio::select! {
                _ = sigterm.recv() => {
                    println!("\n🛑 SIGTERM received - initiating graceful shutdown...");
                }
                _ = sigint.recv() => {
                    println!("\n🛑 SIGINT (Ctrl+C) received - initiating graceful shutdown...");
                }
            }
        }

        #[cfg(windows)]
        {
            use tokio::signal;
            println!("✅ Ctrl+C handler initialized successfully");
            if let Err(e) = signal::ctrl_c().await {
                eprintln!("❌ Failed to listen for Ctrl+C: {}", e);
                return;
            }
            println!("\n� Ctrl+C received - initiating graceful shutdown...");
        }

        // 发送关闭信号
        if let Err(e) = shutdown_tx_clone.send(()) {
            eprintln!("❌ Failed to send shutdown signal: {}", e);
        } else {
            println!("✅ Shutdown signal sent to all components");
        }
    });

    // 创建各个组件
    let websocket_client = BinanceWebSocketClient::new(binance_api_key, binance_secret_key)
        .with_sender(Arc::clone(&message_tx));

    let ai_translator = AITranslator::new()?;
    let bark_notifier = BarkNotifier::new()?;

    // 启动各个组件
    let websocket_handle = {
        let shutdown_rx = shutdown_tx.subscribe();
        tokio::spawn(async move {
            if let Err(e) = websocket_client.run(shutdown_rx).await {
                eprintln!("❌ WebSocket client error: {}", e);
            }
        })
    };

    let ai_handle = {
        let message_rx = message_tx.subscribe();
        let message_tx_clone = Arc::clone(&message_tx);
        let shutdown_rx = shutdown_tx.subscribe();
        tokio::spawn(async move {
            if let Err(e) = ai_translator.run(message_rx, (*message_tx_clone).clone(), shutdown_rx).await {
                eprintln!("❌ AI translator error: {}", e);
            }
        })
    };

    let notification_handle = {
        let message_rx = message_tx.subscribe();
        let shutdown_rx = shutdown_tx.subscribe();
        tokio::spawn(async move {
            if let Err(e) = bark_notifier.run(message_rx, shutdown_rx).await {
                eprintln!("❌ Bark notifier error: {}", e);
            }
        })
    };

    // 等待信号处理器完成（即收到关闭信号）
    if let Err(e) = signal_handle.await {
        eprintln!("❌ Signal handler error: {}", e);
    }

    // 信号已收到，现在等待其他组件优雅关闭，设置超时机制
    println!("⏳ Waiting for all components to shutdown...");

    // 创建一个 future 来等待其他任务完成
    let remaining_tasks = async {
        let result = tokio::try_join!(websocket_handle, ai_handle, notification_handle);
        match result {
            Ok(_) => println!("✅ All components shutdown successfully"),
            Err(e) => eprintln!("❌ Error during shutdown: {}", e),
        }
    };

    // 给组件30秒时间来优雅关闭
    tokio::select! {
        _ = remaining_tasks => {
            // 所有任务正常完成
        }
        _ = tokio::time::sleep(tokio::time::Duration::from_secs(30)) => {
            println!("⚠️ Shutdown timeout reached, forcing exit...");
        }
    }

    println!("👋 Binance WebSocket Notifier shutdown complete");
    Ok(())
}

/// 测试模式：模拟接收公告并测试AI翻译和Bark通知
async fn run_test_mode() -> Result<()> {
    println!("🧪 Starting Test Mode - AI Translation & Bark Notification Test");
    println!("📋 This will simulate receiving a Binance announcement and test the full pipeline");
    println!("{}", "=".repeat(60));

    // 创建消息通道
    let (message_tx, _) = broadcast::channel::<Message>(1000);
    let message_tx = Arc::new(message_tx);

    // 创建AI翻译器和Bark通知器
    let ai_translator = AITranslator::new()?;
    let bark_notifier = BarkNotifier::new()?;

    // 创建关闭信号通道
    let (shutdown_tx, _) = broadcast::channel(1);

    // 启动AI翻译器
    let ai_handle = {
        let message_rx = message_tx.subscribe();
        let message_tx_clone = Arc::clone(&message_tx);
        let shutdown_rx = shutdown_tx.subscribe();
        tokio::spawn(async move {
            if let Err(e) = ai_translator.run(message_rx, (*message_tx_clone).clone(), shutdown_rx).await {
                eprintln!("❌ AI translator error: {}", e);
            }
        })
    };

    // 启动Bark通知器
    let notification_handle = {
        let message_rx = message_tx.subscribe();
        let shutdown_rx = shutdown_tx.subscribe();
        tokio::spawn(async move {
            if let Err(e) = bark_notifier.run(message_rx, shutdown_rx).await {
                eprintln!("❌ Bark notifier error: {}", e);
            }
        })
    };

    // 等待组件启动
    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

    // 创建WebSocket客户端并发送测试公告
    let websocket_client = BinanceWebSocketClient::new("test".to_string(), "test".to_string())
        .with_sender(Arc::clone(&message_tx));

    println!("📤 Sending test announcement...");
    websocket_client.send_test_announcement().await?;

    // 等待处理完成
    println!("⏳ Waiting for AI translation and Bark notification...");
    tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;

    // 发送关闭信号
    println!("🛑 Test completed, shutting down...");
    let _ = shutdown_tx.send(());

    // 等待任务完成
    let _ = tokio::try_join!(ai_handle, notification_handle);

    println!("✅ Test mode completed successfully!");
    println!("💡 If you received a Bark notification, the system is working correctly!");

    Ok(())
}

/// 设置信号处理器的辅助函数
#[cfg(unix)]
async fn setup_signal_handlers() -> Result<()> {
    use tokio::signal::unix::{signal, SignalKind};

    // 测试信号处理器是否能正常工作
    let _sigterm = signal(SignalKind::terminate())
        .map_err(|e| format!("Failed to setup SIGTERM handler: {}", e))?;
    let _sigint = signal(SignalKind::interrupt())
        .map_err(|e| format!("Failed to setup SIGINT handler: {}", e))?;

    println!("🔧 Signal handlers test: OK");
    Ok(())
}
